[1751000813.106946] [WARN]   Logger initialized successfully
[1751000813.107928] [INFO]   === Koneko Rust Implementation Starting ===
[1751000813.108210] [INFO]   !!! Sandbox checks disabled via command-line flag !!!
[1751000813.108455] [INFO]   !!! This is for testing purposes only !!!
[1751000813.108659] [INFO]   Initializing global variables
[1751000813.108888] [INFO]   Collecting call r12 gadgets
[1751000813.176793] [INFO]   Skipping sandbox/VM check (disabled via command-line flag)
[1751000813.177138] [INFO]   Starting main functionality
[1751000813.177280] [INFO]   Entering run_me() function
[1751000813.177508] [INFO]   Skipping KUSER_SHARED_DATA checks (disabled via command-line flag)
[1751000813.177672] [INFO]   Skipping VDLL / Defender emulator check (disabled via command-line flag)
[1751000813.177831] [INFO]   Skipping debugger detection (disabled via command-line flag)
[1751000813.178037] [INFO]   Starting shellcode deobfuscation and preparation
[1751000813.178240] [INFO]   Deobfuscating shellcode using the original Koneko approach
[1751000813.178437] [INFO]   Shellcode deobfuscation complete: 392 bytes
[1751000813.178663] [INFO]   Allocating memory for shellcode
[1751000813.253660] [INFO]   Using NtAllocateVirtualMemory for shellcode allocation
[1751000813.253955] [INFO]   Verifying memory protection flags after allocation for 100% fidelity with original Koneko C++ implementation
[1751000813.331566] [ERROR]  ❌ Memory does NOT have PAGE_EXECUTE_READWRITE protection! Current protection: 0x0
[1751000813.331872] [ERROR]  This does not match the original Koneko C++ implementation which requires PAGE_EXECUTE_READWRITE (0x40)
[1751000813.332027] [INFO]   Writing shellcode to allocated memory
[1751000813.332214] [INFO]   Using WriteProcessMemory for direct byte-for-byte copying of shellcode
[1751000813.332848] [INFO]   Writing shellcode even with sandbox checks disabled
[1751000813.333105] [INFO]   Writing shellcode with WriteProcessMemory for 100% fidelity
[1751000813.333495] [ERROR]  WriteProcessMemory failed with error code: 998
